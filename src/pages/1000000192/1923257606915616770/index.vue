<template>
  <div id="page">
    <div class="container">
      <!-- 活动规则 -->
      <div class="rule-button" @click="onClickRule"></div>
      <!-- 我的奖品 -->
      <div class="my-prize-button" @click="onClickMyPrize"></div>
      <!-- 活动时间 -->
      <div class="activity-time">活动时间:{{ activityTimeText }}</div>
      <!-- 倒计时 -->
      <div class="count-down">
        <div class="count-down-label" :class="activityStatus"></div>
        <van-count-down :time="countDowntime" v-slot="{ days, hours, minutes, seconds }">
          <div class="count-down-timer">
            <div class="count-down-value days">
              <span>{{ String(days).padStart(2, '0').split('')[0] }}</span>
              <span>{{ String(days).padStart(2, '0').split('')[1] }}</span>
            </div>
            <div class="count-down-value hours">
              <span>{{ String(hours).padStart(2, '0').split('')[0] }}</span>
              <span>{{ String(hours).padStart(2, '0').split('')[1] }}</span>
            </div>
            <div class="count-down-value minutes">
              <span>{{ String(minutes).padStart(2, '0').split('')[0] }}</span>
              <span>{{ String(minutes).padStart(2, '0').split('')[1] }}</span>
            </div>
            <div class="count-down-value seconds">
              <span>{{ String(seconds).padStart(2, '0').split('')[0] }}</span>
              <span>{{ String(seconds).padStart(2, '0').split('')[1] }}</span>
            </div>
          </div>
        </van-count-down>
      </div>
      <!-- sku列表 -->
      <div class="goods-list swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="item in mainData.goods" :key="item?.skuId" :data-skuid="item?.skuId">
            <div class="goods-card">
              <template v-if="item">
                <img class="goods-img" :src="item.skuImg" />
                <div class="goods-tag">自营</div>
                <div class="goods-title text-overflow-2-line">
                  {{ item.skuName }}
                </div>
                <div class="goods-extra">
                  <div class="goods-extra-left">
                    <div class="goods-promo">满赠</div>
                    <div class="goods-price">￥{{ item.skuPrice || 0 }}</div>
                  </div>
                  <div class="goods-add">
                    <span>+</span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <!-- 购物车结算按钮 -->
      <div class="to-my-cart-button" @click="toMyCart"></div>
      <!-- 大转盘 -->
      <div class="lucky-wheel">
        <lucky-wheel ref="myLucky" width="54vw" height="54vw" :blocks="config.blocks" :prizes="config.prizes" :buttons="config.buttons" @start="handleDrawLottery" @end="endCallback" :defaultConfig="config.defaultConfig" />
      </div>
      <!-- 剩余抽奖次数-->
      <span class="remain-draw-chance">剩余抽奖次数: {{ mainData.times }}次</span>
      <!-- 抽奖按钮 -->
      <div class="draw-button" @click="handleDrawLottery"></div>
    </div>
  </div>
  <!-- 入会弹窗 -->
  <van-popup class="my-dialog" :show="openCardDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: openCardDialog,
        animate__zoomOut: !openCardDialog,
      }"
      @click.stop>
      <div class="open-card-dialog">
        <!-- 加入会员按钮 -->
        <div class="button" @click="handleOpenCard"></div>
      </div>
    </div>
  </van-popup>
  <!-- 规则弹窗 -->
  <van-popup class="my-dialog" :show="showRuleDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showRuleDialog,
        animate__zoomOut: !showRuleDialog,
      }"
      @click.stop>
      <div class="rule-dialog">
        <div class="rule-dialog-container">
          <p class="rule-text">{{ ruleText }}</p>
        </div>
        <!-- 我知道了按钮 -->
        <div class="close-rule-dialog-button" @click="showRuleDialog = false"></div>
      </div>
      <!-- 关闭按钮 -->
      <div class="dialog-close-btn" @click="showRuleDialog = false"></div>
    </div>
  </van-popup>
  <!-- 我的奖品弹窗 -->
  <van-popup class="my-dialog" :show="showMyPrizeDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showMyPrizeDialog,
        animate__zoomOut: !showMyPrizeDialog,
      }"
      @click.stop>
      <div class="my-prize-dialog">
        <div class="my-prize-container">
          <span v-if="myPrizeList.length === 0" class="empty-text">暂无记录</span>
          <div v-else class="my-prize-item" v-for="(item, index) in myPrizeList" :key="index">
            <span class="col-time">{{ dayjs(item.drawPrizeDate).format('YYYY-M-D') }}</span>
            <span class="col-type"> 京豆 </span>
            <span class="col-name">{{ item.prizeName }}</span>
          </div>
        </div>
        <!-- 返回按钮 -->
        <div class="close-my-prize-dialog-button" @click="showMyPrizeDialog = false"></div>
      </div>
      <!-- 关闭按钮 -->
      <div class="dialog-close-btn" @click="showMyPrizeDialog = false"></div>
    </div>
  </van-popup>
  <!-- 未中奖弹窗 -->
  <van-popup class="my-dialog" :show="showPrizeNoResDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showPrizeNoResDialog,
        animate__zoomOut: !showPrizeNoResDialog,
      }"
      @click.stop>
      <div class="no-prize-res-dialog">
        <div class="button" @click="showPrizeNoResDialog = false"></div>
      </div>
    </div>
  </van-popup>
  <!-- 中奖弹窗 -->
  <van-popup class="my-dialog" :show="showPrizeResDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showPrizeResDialog,
        animate__zoomOut: !showPrizeResDialog,
      }"
      @click.stop>
      <div class="prize-res-dialog">
        <div class="text">
          {{ `恭喜您抽中${drawRes.result.cardNumber}个京豆!\n可以继续参与免单抽奖哦!` }}
        </div>
        <div class="button" @click="showPrizeResDialog = false"></div>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { inject, reactive, provide, computed, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { closeToast, showLoadingToast, showToast, Swipe as VanSwipe, SwipeItem as VanSwipeItem, Popup as VanPopup, CountDown as VanCountDown } from 'vant';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';

import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import openCard from '@/utils/openCard';

import config from './config';

Swiper.use([Autoplay]); // 使用Swiper的扩展模块

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('baseInfo', baseInfo);

const pathParams: any = inject('pathParams');
console.log('pathParams', pathParams);

const baseUserInfo: any = inject('baseUserInfo');
console.log('baseUserInfo', baseUserInfo);

const decoData: any = inject('decoData');
console.log('decoData', decoData);

const delayToast = (e: string, time = 1000): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      showToast(e);
      resolve();
    }, time);
  });
};

const mainData = reactive({
  activityStartTime: '',
  activityEndTime: '',
  currentTime: '',
  goods: [
    /*  {
      addCart: 0,
      skuId: 100222448294,
      skuImg: 'http://img14.360buyimg.com/n0/jfs/t1/289192/32/3754/39422/681b21a9F9e65a2b6/079b8dbeb0d6bf24.jpg',
      skuName: '金士顿（Kingston）128GB USB3.2 Gen 1 U盘 DTXS 缤纷多彩旋转U盘 时尚便携 学习办公投标电脑通用',
      skuSort: 1,
    }, */
    null,
    null,
    null,
    null,
  ],
  times: 0,
});

const activityTimeText = computed(() => {
  // 判断数据是否有效
  if (!mainData.activityStartTime || !mainData.activityEndTime) return '';

  const start = dayjs(mainData.activityStartTime).format('MM月DD日');
  const end = dayjs(mainData.activityEndTime).format('MM月DD日');
  return `${start}-${end}`;
});

const activityStatus = computed(() => {
  if (!mainData.activityStartTime || !mainData.activityEndTime || !mainData.currentTime) return 'loading';
  const start = dayjs(mainData.activityStartTime).valueOf();
  const end = dayjs(mainData.activityEndTime).valueOf();
  const now = dayjs(mainData.currentTime).valueOf();
  if (now < start) return 'not-started';
  if (now >= start && now < end) return 'started';
  return 'ended';
});

// 倒计时
const countDowntime = ref(0);
function updateCountdown() {
  if (!mainData.activityStartTime || !mainData.activityEndTime || !mainData.currentTime) {
    countDowntime.value = 0;
    return;
  }
  const start = dayjs(mainData.activityStartTime).valueOf();
  const end = dayjs(mainData.activityEndTime).valueOf();
  const now = dayjs(mainData.currentTime).valueOf();
  if (now < start) {
    countDowntime.value = start - now; // 距离开始
  } else if (now >= start && now < end) {
    countDowntime.value = end - now; // 距离结束
  } else {
    countDowntime.value = 0; // 已结束
  }
}
watch([() => mainData.activityStartTime, () => mainData.activityEndTime, () => mainData.currentTime], updateCountdown, { immediate: true });

let goodsSwiper: Swiper | null = null;
watch(
  () => mainData.goods,
  (val) => {
    nextTick(() => {
      if (goodsSwiper) {
        goodsSwiper.destroy(true, true); // 销毁旧的
        goodsSwiper = null;
      }
      if (val.length) {
        goodsSwiper = new Swiper('.goods-list.swiper-container', {
          slidesPerView: 4,
          loop: val.length > 4,
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          // 加上这两行，防止 swiper 阻止 click
          preventClicks: false,
          preventClicksPropagation: false,
          on: {
            tap: async (swiper, e) => {
              await checkActivityTime();
              const target = e.target as HTMLElement;
              const slideEl = swiper.slides[swiper.clickedIndex];
              if (slideEl) {
                const skuId = slideEl.getAttribute('data-skuid');
                console.log('skuId:', skuId);
                if (!skuId) return;
                if (target.closest('.goods-add')) {
                  console.log('点击加号按钮');
                  handleAddCart(skuId);
                  return;
                }
                console.log('点击整个');
                gotoSkuPage(skuId);
              }
            },
          },
        });
      }
    });
  },
  { immediate: true },
);
// 跳转购物车
const toMyCart = async () => {
  await checkActivityTime();

  window.jmfe.toMyCart();
};
// 加购商品逻辑
const handleAddCart = async (skuId) => {
  console.log('🚀 ~ handleAddCart ~ skuId:', skuId);
  if (!window.jmfe.isApp('jd')) {
    showToast('请在京东APP中进行加购');
    return;
  }

  addSkuToCart(skuId);
};

// 获取活动主接口返回信息
const getHomeData = async (options = { showLoading: true, onlyUpdateTime: false }) => {
  const { showLoading = true, onlyUpdateTime = false } = options;

  try {
    if (showLoading) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    const { data } = await httpRequest.post('/kingston/1914959045256859650/loading/main');
    if (onlyUpdateTime) {
      mainData.times = data.times;
    } else {
      mainData.activityStartTime = data.activityStartTime;
      mainData.activityEndTime = data.activityEndTime;
      mainData.currentTime = data.currentTime;
      mainData.goods = data.goods;
      mainData.times = data.times;
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    if (showLoading) {
      closeToast();
    }
  }
};

// 抽奖逻辑
const myLucky = ref();
// 中奖信息
const drawRes = ref(null);
// 未中奖弹窗
const showPrizeNoResDialog = ref(false);
// 中奖弹窗
const showPrizeResDialog = ref(false);
// 抽奖逻辑
const drawLock = ref(false);
const handleDrawLottery = async () => {
  await checkActivityTime();

  if (mainData.times <= 0) {
    showToast('暂无抽奖机会');
    return;
  }

  if (drawLock.value) return; // 加锁中，阻止点击
  drawLock.value = true;

  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();

  try {
    const { data } = await httpRequest.post('/kingston/1914959045256859650/draw');

    await new Promise((resolve) => setTimeout(resolve, 3000));

    if (data.status === 0) {
      drawRes.value = null;
      myLucky.value.stop(1);
    } else {
      drawRes.value = data;
      myLucky.value.stop(0);
    }
  } catch (e: any) {
    await new Promise((resolve) => setTimeout(resolve, 3000));
    drawRes.value = null;
    myLucky.value.stop(1);
  } finally {
  }
};

// 抽奖结束会触发end回调
const endCallback = async (prize) => {
  await new Promise((resolve) => setTimeout(resolve, 500));

  if (drawRes.value) {
    showPrizeResDialog.value = true;
  } else {
    showPrizeNoResDialog.value = true;
  }

  // 更新抽奖次数
  getHomeData({
    showLoading: false,
    onlyUpdateTime: true,
  });

  drawLock.value = false; // ✅ 解锁
};

// 入会弹窗
const openCardDialog = ref(false);
watch(
  () => baseInfo?.memberLevel,
  (val) => {
    if (val == null) {
      openCardDialog.value = true;
    }
  },
  { immediate: true },
);
// 入会
const handleOpenCard = async () => {
  await checkActivityTime();
  const openCardUrl = 'https://shopmember.m.jd.com/shopcard/?venderId=1000000192&shopId=1000000192&channel=801';
  openCard(`${openCardUrl}&returnUrl=${`${window.location.href}&isJoin=1`}`);
};

const checkActivityTime = async () => {
  return new Promise((resolve, reject) => {
    if (activityStatus.value === 'not-started') {
      showToast('活动未开始');
      reject(false);
    } else if (activityStatus.value === 'ended') {
      showToast('活动已结束');
      reject(false);
    } else {
      resolve(true);
    }
  });
};

// 规则弹窗
const showRuleDialog = ref(false);
const ruleText = ref('');
// 点击活动规则按钮
const onClickRule = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.get('/common/getRule');
    ruleText.value = res.data || '';
    showRuleDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};

// 我的奖品弹窗
const showMyPrizeDialog = ref(false);
// 我的奖品列表
const myPrizeList = ref([]);
// 点击我的奖品按钮
const onClickMyPrize = async () => {
  await checkActivityTime();
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.get('/common/prizes');
    myPrizeList.value = res.data || [];
    showMyPrizeDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};

onMounted(async () => {
  // 滚动监听
  const scrollKey = 'page-scroll-top';
  const pageEl = document.getElementById('page');
  if (pageEl) {
    const handleScroll = debounce(() => {
      sessionStorage.setItem(scrollKey, String(pageEl.scrollTop));
    }, 200);
    pageEl.addEventListener('scroll', handleScroll);

    // 恢复滚动位置
    const savedTop = sessionStorage.getItem(scrollKey);
    pageEl.scrollTop = Number(savedTop);
  }

  await getHomeData();
});
</script>

<style lang="scss" scoped>
#page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
  // font-family: SourceHanSansCN-Bold;
}

/* 活动规则 */
.rule-button {
  position: absolute;
  top: 0.8rem;
  right: 0;
  width: (260 / (2 * 100)) * 1rem;
  height: (80 / (2 * 100)) * 1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/302437/8/5645/4370/6826df4dFb6cae9ee/802368d62a34074a.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 我的奖品 */
.my-prize-button {
  position: absolute;
  top: 0.3rem;
  right: 0;
  width: (260 / (2 * 100)) * 1rem;
  height: (80 / (2 * 100)) * 1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/301004/13/7032/4491/6826df4dFe769f219/7fedf3f6a20581ae.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: (4360 / (2 * 100)) * 1rem;
  padding-bottom: 1rem;
  background-color: #fffaf1;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/291472/36/8531/1010788/682efeb9F81d15f38/970277dd77d7cb58.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

.activity-time {
  position: absolute;
  top: 2.75rem;
  left: 50.2%;
  display: inline-block;
  min-width: 4rem;
  color: #fff;
  font-size: 0.3rem;
  white-space: nowrap;
  transform: translateX(-50%) skew(0deg, -3.4deg);
}

.count-down {
  position: absolute;
  top: 4.8rem;
  left: 0.48rem;
  display: flex;
  align-items: center;

  .count-down-label {
    width: (393 / (3 * 100)) * 1rem;
    height: (203 / (3 * 100)) * 1rem;
    margin-right: 0.45rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;

    &.not-started {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/320760/25/2012/9496/682beea3F0a28c735/a27a1fe8e34bb74c.png');
    }

    &.started,
    &.ended {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/301064/18/8162/9369/682beea3Ffe34313d/f52a24f2987b96db.png');
    }
  }

  .count-down-timer {
    display: flex;

    .count-down-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 0.7rem;
      height: 0.52rem;
      color: #942603;
      font-size: 0.38rem;

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 0.34rem;
        height: 100%;
      }
    }

    .days {
      margin-right: 0.55rem;
    }

    .hours {
      margin-right: 0.57rem;
    }

    .minutes {
      margin-right: 0.57rem;
    }

    .seconds {
    }
  }
}

.goods-list {
  position: absolute;
  top: 9.7rem;
  left: 0.3rem;
  width: 93vw;
  overflow: hidden;

  .goods-card {
    position: relative;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 1.65rem;
    min-height: 2.6rem;
    margin-right: 0.1rem;
    padding: 0.08rem;
    background: #fff;
    border-radius: 0.2rem;
    box-shadow: 0 2px 8px #e5d1b7;
  }

  .goods-img {
    height: 1.5rem;
    margin-bottom: 0.08rem;
    object-fit: contain;
    border-top-left-radius: 0.2rem;
    border-top-right-radius: 0.2rem;
  }

  .goods-tag {
    position: absolute;
    top: 1.67rem;
    left: 0.1rem;
    padding: 0.03rem;
    color: #fff;
    font-size: 0.12rem;
    font-family: sans-serif;
    letter-spacing: 0.01rem;
    background: #fa5151;
    border-radius: 0.02rem;
  }

  .goods-title {
    color: #222;
    font-weight: bold;
    font-size: 0.16rem;
    font-family: sans-serif;
    line-height: 1.3;
    text-indent: 2.6em;
  }

  .goods-extra {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 0.05rem;
  }

  .goods-extra-left {
    display: flex;
    align-items: center;
  }

  .goods-promo {
    display: none;
    margin-right: 0.02rem;
    color: #fa5151;
    font-size: 0.16rem;
    font-family: sans-serif;
  }

  .goods-price {
    color: #fa5151;
    font-weight: bold;
    font-size: 0.16rem;
    font-family: sans-serif;
  }

  .goods-add {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.38rem;
    height: 0.38rem;
    padding-bottom: 0.03rem;
    color: #fff;
    font-size: 0.22rem;
    background: #fa5151;
    border-radius: 50%;
    cursor: pointer;
  }
}

.to-my-cart-button {
  position: absolute;
  top: 12.4rem;
  width: 2.2rem;
  height: 0.8rem;
}

.lucky-wheel {
  position: absolute;
  top: 15.24rem;
  left: 50%;
  transform: translateX(-50%);
}

.remain-draw-chance {
  position: absolute;
  top: 19.56rem;
  color: #942603;
  font-size: 0.24rem;
}

.draw-button {
  position: absolute;
  top: 20.3rem;
  width: 2.2rem;
  height: 0.8rem;
}

/* 弹窗 */
.my-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 7.5rem;
  overflow-y: unset;
  background: none;

  &.van-popup--center {
    max-width: 100%;
  }
}

.dialog-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
}

/* 关闭弹窗icon */
.dialog-close-btn {
  width: (93 / (2 * 100)) * 1rem;
  height: (93 / (2 * 100)) * 1rem;
  margin-top: 0.3rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/291469/17/7683/3426/682bf7f9F5b9608e7/301c8bf36fecf8f1.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 入会弹窗 */
.open-card-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1500 / (2 * 100)) * 1rem;
  height: (1538 / (2 * 100)) * 1rem;
  padding-top: 1.72rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/312211/23/2522/338929/682c4152F5b94f912/c52bec746b8b9438.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .button {
    width: 2.57rem;
    height: 1.17rem;
    margin-top: 3.7rem;
  }
}

/* 规则弹窗 */
.rule-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1079 / (2 * 100)) * 1rem;
  height: (1231 / (2 * 100)) * 1rem;
  padding-top: 1.72rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/320324/27/2259/168708/682bf7f9F4c22614d/f3e88efb5fadb23f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .rule-dialog-container {
    position: relative;
    left: 0.08rem;
    width: 5rem;
    height: 2.68rem;
    padding: 0.1rem;
    overflow: scroll;

    .rule-text {
      color: #fff;
      font-size: 0.2rem;
      line-height: 1.5;
      white-space: pre-line;
      text-align: justify;
      word-break: break-all;
    }
  }

  .close-rule-dialog-button {
    width: 2.57rem;
    height: 1.17rem;
    margin-top: 0.4rem;
  }
}

/* 我的奖品弹窗 */
.my-prize-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1054 / (2 * 100)) * 1rem;
  height: (1377 / (2 * 100)) * 1rem;
  padding-top: 3.1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/313256/33/2519/173947/682c189aFe041bd05/989b340761679bcf.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .my-prize-container {
    position: relative;
    width: 5rem;
    height: 2.08rem;
    padding: 0 0.1rem;
    overflow: scroll;
    color: #fff;
    font-size: 0.2rem;
  }

  .my-prize-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.1rem 0;
  }

  .col-time {
    flex: 0 0 1.6rem;
    text-align: center;
  }

  .col-type {
    flex: 0 0 1.2rem;
    text-align: center;
  }

  .col-name {
    display: flex;
    flex: 1;
    justify-content: center;
  }

  .btn {
    width: 1.25rem;
    height: 0.48rem;
    font-size: 0.2rem;
    border-radius: 0.25rem;
  }

  .btn.blue {
    color: #fff;
    background-image: linear-gradient(90deg, #88caff 0%, #7ec5fe 71%, #74c0fd 100%);
  }

  .btn.gray {
    color: #8e8e8e;
    background-color: #e3e3e3;
  }

  .close-my-prize-dialog-button {
    width: 2.57rem;
    height: 1.17rem;
    margin-top: 0.4rem;
  }
}

/* 未中奖弹窗 */
.no-prize-res-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1054 / (2 * 100)) * 1rem;
  height: (1377 / (2 * 100)) * 1rem;
  padding-top: 1.72rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/305544/20/3639/205895/682c4413F61211867/cd9ecfa9a662eba1.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .button {
    width: 2.57rem;
    height: 1.17rem;
    margin-top: 3.7rem;
  }
}

/* 中奖弹窗 */
.prize-res-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1500 / (2 * 100)) * 1rem;
  height: (1552 / (2 * 100)) * 1rem;
  padding-top: 1.72rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/304235/1/3659/288326/682c468bFc6ab99e8/b5ac95a7c7670d32.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .text {
    position: absolute;
    top: 3.95rem;
    color: #df002b;
    font-size: 0.3rem;
    line-height: 1.3;
    white-space: break-spaces;
    text-align: center;
  }

  .button {
    width: 2.57rem;
    height: 1.17rem;
    margin-top: 3.7rem;
  }
}

.empty-text {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 0.26rem;
  transform: translate(-50%, -50%);
}
</style>

<style lang="scss">
@font-face {
  font-weight: bold;
  font-family: SourceHanSansCN-Bold;
  font-style: normal;
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Bold.otf') format('opentype');
  font-display: swap;
}

* {
  line-height: 1;

  ::-webkit-scrollbar {
    display: none;
  }
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior: none none;
}

button {
  margin: 0;
  padding: 0;
  background-color: transparent;
  border: 0;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0%); /* 透明色，禁用默认高亮效果 */
}

.text-overflow-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-overflow-2-line {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
