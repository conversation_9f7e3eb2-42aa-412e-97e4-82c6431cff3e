import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import { getActData } from './ts/logic';

// 获取 <html> 标签放一张图兜底
const htmlTag = document.getElementsByTagName('html')[0];
htmlTag.style.backgroundImage = "url('//img10.360buyimg.com/imgzone/jfs/t1/314806/28/6936/205388/6840f948Fee05ab08/8889a87090411e13.png')";
htmlTag.style.backgroundSize = "cover";
htmlTag.style.backgroundRepeat = "no-repeat";
htmlTag.style.backgroundColor = "#ebd2bb";


initRem();
const app = createApp(index);

// 初始化页面
const config: InitRequest = {
  urlPattern: '/custom/:activityType/:templateCode',
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
