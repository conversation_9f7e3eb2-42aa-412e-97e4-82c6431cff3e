<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="prizeBg" :style="actData.status === 1 ? furnishStyles.giftImgLocked.value : furnishStyles.giftImg.value">
      <!--已领取-->
      <div class="btn" v-if="actData.status === 1" :style="furnishStyles.lockedBtnImg.value" @click="toLock"/>
      <!--立即申请-->
      <div class="btn" v-else :style="furnishStyles.receiveBtnImg.value" v-click-track="'ljsq'" @click="toLock"/>
    </div>
    <img class="ruleBox" :src="furnish.ruleBox" alt=""/>
  </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { DecoData } from '@/types/DecoData';
import {actData, getActData} from './ts/logic';
import dayjs from 'dayjs';
import {httpRequest} from "@/utils/service";

const decoData = inject('decoData') as DecoData;

// 点击锁权
const toLock = async () => {
  if (dayjs().isBefore(actData.value.promotionStartTime)) {
    showToast('还未到申领时间哦~');
    return;
  }
  if (dayjs().isAfter(actData.value.promotionEndTime)) {
    showToast('申领已经截止了哦~');
    return;
  }
  closeToast();
  // 已经已领取
  if (actData.value.status === 1) {
    showToast('您已经领取过奖品了~');
    return;
  }
  try {
    // 为 receiveToken 接口添加 loading
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/96006/receiveToken');
    closeToast();
    // 领取成功后刷新数据
    await getActData();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    setTimeout(()=>{
      closeToast();
    },2000)
  }
};

// 拉sku列表数据
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActData()]);
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-top: 0.5rem;
  background-color: #ebd2bb;
}
.prizeBg {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 6.09rem;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  .btn {
    background: no-repeat;
    background-size: 100%;
    width: 2rem;
    height: 0.6rem;
    position: absolute;
    bottom: 0.7rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
.ruleTitle{
  width: 2.5rem;
  margin: 0.3rem auto;
  text-align: center;
}
.ruleBox{
  width: 6.9rem;
  background: no-repeat;
  background-size: 100%;
  margin: 0.2rem auto 0;
  padding: 0 0 0.4rem;
}
</style>
