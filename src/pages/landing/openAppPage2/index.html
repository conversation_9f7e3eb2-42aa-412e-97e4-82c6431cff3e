<!DOCTYPE html>
<html style="font-size: 100px;">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
  <meta http-equiv="expires" content="-1">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <title>打开APP参与活动</title>
  <!-- <link rel="stylesheet" href="//cjwxqz.dianpusoft.cn/resources/wxjs/gallery/ibd/ibd.min.css?6894"> -->
  <style>
    a,
    abbr,
    acronym,
    address,
    applet,
    article,
    aside,
    audio,
    b,
    big,
    blockquote,
    body,
    button,
    canvas,
    caption,
    center,
    cite,
    code,
    dd,
    del,
    details,
    dfn,
    div,
    dl,
    dt,
    em,
    embed,
    fieldset,
    figcaption,
    figure,
    footer,
    form,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    header,
    hgroup,
    html,
    i,
    iframe,
    img,
    ins,
    kbd,
    label,
    legend,
    li,
    mark,
    menu,
    nav,
    object,
    ol,
    output,
    p,
    pre,
    q,
    ruby,
    s,
    samp,
    section,
    small,
    span,
    strike,
    strong,
    sub,
    summary,
    sup,
    table,
    tbody,
    td,
    tfoot,
    th,
    thead,
    time,
    tr,
    tt,
    u,
    ul,
    var,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      vertical-align: baseline;
      background: 0 0;
      outline: 0;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      display: block
    }

    ol,
    ul {
      list-style: none
    }

    button {
      background: 0 0
    }

    blockquote,
    q {
      quotes: none
    }

    blockquote:after,
    blockquote:before,
    q:after,
    q:before {
      content: '';
      content: none
    }

    strong {
      font-weight: 700
    }

    table {
      border-collapse: collapse;
      border-spacing: 0
    }

    img {
      border: 0;
      max-width: 100%
    }

    html {
      line-height: initial
    }

    body {
      font-size: .32rem;
      font-family: "Helvetica Neue", Helvetica, sans-serif
    }

    a {
      text-decoration: none
    }
  </style>
  <!--处理不同页面大小兼容的方法，必须放在最上面-->
  <!-- <script src="//cjwxqz.dianpusoft.cn/resources/wxjs/htmlFontSize.min.js?6894"></script> -->
  <script>
    !function (n, e) {
      var t = n.documentElement,
        i = 'orientationchange' in window ? 'orientationchange' : 'resize',
        d = function () {
          var n = t.clientWidth;
          n && (t.style.fontSize = n >= 750 ? '100px' : n / 750 * 100 + 'px');
        };
      n.addEventListener && (e.addEventListener(i, d, !1), n.addEventListener('DOMContentLoaded', d, !1));
    }(document, window);

    var t = document.documentElement;
    var n = t.clientWidth;
    n && (t.style.fontSize = n >= 750 ? '100px' : n / 750 * 100 + 'px');
  </script>

  <script type="text/javascript" src="https://lzcdn.dianpusoft.cn/zepto/zepto.min.js"></script>
  <script type="text/javascript" src="https://lzcdn.dianpusoft.cn/qrcode/qrcode.min.js"></script>
  <script type="text/javascript" src="https://lzcdn.dianpusoft.cn/clipboard/clipboard.min.js"></script>
  <style>
    #app {
      position: relative;
      max-width: 750px;
      margin: 0 auto;
    }

    #opentitle {
      position: relative;
      width: 7.5rem;
      margin: 0 auto;
    }

    #qrcodegroup {
      position: fixed;
      left: -99999px;
    }

    #qrcode {
      position: relative;
      width: 4rem;
      height: 4rem;
      margin: 0.2rem auto;
    }

    .textdiv {
      position: relative;
      width: 6.5rem;
      margin: 0.2rem auto;
      font-size: 0.28rem;
      color: #8a8a8a;
    }

    .bluetip {
      position: relative;
      width: 5rem;
      height: 0.6rem;
      border-radius: 0.3rem;
      margin: 0.2rem auto;
      font-size: 0.24rem;
      background: #2d8cf0;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    #valueinput {
      position: relative;
      width: 6.5rem;
      margin: 0.2rem auto;
      display: block;
    }

    #cliptxt {
      color: #1a7fd4;
      border: 1px solid #1a7fd4;
      border-radius: 6rem;
      height: 0.72rem;
      border-radius: 0.36rem;
      width: 2rem;
      text-align: center;
      margin: 0.2rem auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    #jumptoshop {
      display: none;
      color: #fff;
      background: #1a7fd4;
      border-radius: 0.1rem;
      width: 2rem;
      font-size: 0.3rem;
      padding: 0.2rem;
      text-align: center;
      margin: 0.3rem auto;
    }
  </style>
</head>

<body>
<div id="app">
  <div id="opentitle">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/61969/13/3552/18361/5d1c37a2E6a1811d2/b3a056ac7eea7a26.jpg">
  </div>
  <div class="textdiv">亲爱的，由于您的浏览器限制，无法直接启动京东APP打开链接，需要您选择以下方式进行打开：</div>
  <div class="bluetip">用京东APP扫一扫打开</div>
  <div id="textdiv2" class="textdiv">步骤一：长按二维码，保存二维码到相册。</div>
  <div id="qrcodegroup"></div>
  <div id="qrcode"><img src=""></div>
  <div id="textdiv3" class="textdiv">步骤二：打开京东APP，点击"扫啊扫"选相册中的二维码图片，打开链接。</div>
  <!--    <div class="bluetip">方式二：复制下面连接在QQ或微信中打开</div>-->
  <input type="hidden" id="valueinput" value=""/>
  <!--    <div id="cliptxt" data-clipboard-action="copy" style="cursor: pointer" onclick=""-->
  <!--      data-clipboard-target="#valueinput">复制链接</div>-->
  <!--    <div id="jumptoshop">进店逛逛</div>-->
</div>
<script>
  function mytoast(n, a) {
    var i = $('<div class="ibd-toast" style="left:50%;max-width:90%;transform:translateX(-50%);position: fixed;top: 45%;z-index: 2010000;background: #000;color: #fff;font-size: 0.3rem;padding: 5px 10px;border-radius: 17px;-webkit-transition: all 500ms ease-in-out;-moz-transition: all 500ms ease-in-out;-o-transition: all 500ms ease-in-out;transition: all 500ms ease-in-out;opacity: 0;word-break: break-all;">'
      + n + '</div>');
    $('body')
      .append(i);
    setTimeout(function () {
      i.addClass('active');
    }), setTimeout(function () {
      i.remove();
    }, a || 2e3);
  }

  /*获取url地址栏中参数值*/
  function getUrlQueryValueByKey(url, keyName) {
    var reg = new RegExp('(^|[&?])' + keyName + '=([^&]*)(&|$)');
    var r = url.match(reg);//search,查询？后面的参数，并匹配正则
    if (r != null) {
      return unescape(r[2]);
    }
    return null;
  }

  //key名称为actlink 必须经过encodeURIComponent
  var acturl = getUrlQueryValueByKey(window.location.href, 'actlink');
  // var acturl = localStorage.getItem('actlink') || '';
  acturl = acturl.replace('-isv.isvjd', '-isv.isvjcloud');
  var acturldecode = decodeURIComponent(acturl);
  var targetUrl = new URL(acturldecode);
  var hostnamne = targetUrl.hostname;

  /**
   * 判断是否是PC端(区分pc端模拟)
   * */
  function isPC() {
    // 检测平台
    const p = navigator.platform;
    const pc1 = p.indexOf('Win') === 0;
    const pc2 = p.indexOf('Mac') === 0;
    return pc1 || pc2;
  }

  function isMobileBrowser() {
    // 定义常见移动设备的关键字
    const mobileKeywords = ['Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone', 'Opera Mini', 'IEMobile'];

    // 遍历关键字数组，判断 userAgent 中是否包含这些关键字
    return mobileKeywords.some(keyword => navigator.userAgent.includes(keyword));
  }

  function init() {
    try {
      makeqrcode(acturldecode, 400);
      clipboardfun(acturldecode);
      const isMobile = isMobileBrowser();
      const isPc = isPC();
      console.log('isPC', isPC());
      document.getElementById('opentitle').style.display = isMobile ? 'block' : 'none';
      document.getElementById('textdiv2').style.display = !isPc ? 'block' : 'none';
      document.getElementById('textdiv3').style.display = !isPc ? 'block' : 'none';
      if (isMobile) {
        $('#opentitle')
          .bind('click', function () {
            openappfun();
          });
        openappfun();
      }
      jumptoh5shop();
    } catch (e) {
      console.error(e);
    }
  }

  function clipboardfun(cliptext) {
    document.getElementById('valueinput').value = cliptext;
    var clipboard = new ClipboardJS('#cliptxt');

    clipboard.on('success', function (e) {
      mytoast('复制成功！');
      e.clearSelection();
    });

    clipboard.on('error', function (e) {
      mytoast('复制失败，请长按复制！');
    });
  }

  function makeqrcode(url, size) {
    var qrcode = new QRCode(document.getElementById('qrcodegroup'), {
      text: url,
      width: size,
      height: size,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: QRCode.CorrectLevel.H
    });
    var qrcodeImgurl = document.querySelector('#qrcodegroup canvas')
      .toDataURL('image/jpeg');

    $('#qrcode img')
      .attr('src', qrcodeImgurl);

  }

  function openappfun() {
    if (hostnamne.indexOf('lzkj') > -1 || hostnamne.indexOf('//3.cn') > -1 || hostnamne.indexOf('jd.com') > -1) {
      try {
        var params = {
          category: 'jump',
          des: 'm',
          url: acturl,
        };
        params = encodeURIComponent(JSON.stringify(params));
        var linkUrl = 'openApp.jdMobile://virtual?params=' + params;
        window.location.href = linkUrl;
      } finally {
      }
    }
  }

  function jumptoh5shop() {
    var shopid = getUrlQueryValueByKey(window.location.href, 'shopid');
    if (shopid) {
      $('#jumptoshop')
        .on('click', function () {
          window.location.href = 'https://shop.m.jd.com/?shopId=' + shopid;
        })
        .show();
    }
  }

  init();
</script>

</body>

</html>
