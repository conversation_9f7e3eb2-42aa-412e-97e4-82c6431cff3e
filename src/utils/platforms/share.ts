import { ShareConfig, SharePosterConfig } from '@/types/ShareConfig';
import DOMAIN, { exchangeSubStr } from './domain';
import { isMiniProgram, wxSdkConfig } from './wx';
import clientType, { getClientType } from './clientType';
import ShareType from './shareType';
import { reportShareEvent } from '../trackEvent/jdReport';
import constant from '../constant';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { showToast } from 'vant';
import _ from 'lodash';
import QRCode from 'qrcode';
import { httpRequest } from '@/utils/service';
import { compressImage } from '@/utils/ImageUtils';

const defaultShareImg = 'http://img10.360buyimg.com/imgzone/jfs/t1/163534/34/4106/101460/600e2292Ed3609887/824e50f6ac5477dd.jpg';
const defaultCMDShareChannel = 'Wxfriends,Wxmoments,QQfriends,QQzone,Sinaweibo';
const defaultH5ShareChannel = 'Wxfriends,Wxmoments,QQfriends,QQzone';
const defaultMpShareChannel = 'Wxfriends';

const debounceShareCallBack = (shareConfig: any) => {
  shareConfig.clickcallback = window.jmfe.isAndroid() ? _.debounce(shareConfig.clickcallback, 300, {
    leading: true, // 延长开始后调用
    trailing: false, // 延长结束前调用
  }) : null;
  shareConfig.callback = window.jmfe.isIOS() ? shareConfig.callback : null;
  return shareConfig;

};

// 获取京口令分享配置
const getCMDShareConfig = (config: ShareConfig = {}) => {
  console.log('getCMDShareConfig');
  const title = config.title || '请您参与活动';
  const content = config.content || title;
  const originUrl = config.shareUrl || window.location.href;
  const url = exchangeSubStr(originUrl, DOMAIN.PROTECT, DOMAIN.COMMON);
  const img = config.imageUrl || defaultShareImg;
  const endTime = Date.now() + 3 * 60 * 60 * 24 * 1000;
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const clickcallback = (() => {
    console.log('click callback');
    lzReportClick('v_head_share_click');
    config.afterShare && config.afterShare();
  });
  const channel = config.channel || defaultCMDShareChannel;
  const openAppParams = {
    category: 'jump',
    des: 'jdreactcommon',
    modulename: 'JDReactCollectJDBeans',
    appname: 'JDReactCollectJDBeans',
    param: {
      page: 'collectJDBeansHomePage',
      transparentenable: true,
    },
  };
  // 口令参数集合,由客户端透传h5参数，便于拓展,类型字符串
  const keyparam = {
    url, // 分享url，类型字符串,V8.3.6add，必传，与keyOpenapp二者至少填写一个，两个都传，优先支持openAppp,h5作为兜底
    keyEndTime: JSON.stringify(endTime), // 活动结束时间，类型字符串,V8.3.6add,单位：毫秒，必传
    keyChannel: channel, // 分享渠道 //分享渠道，客户端使用，服务端无用 ，类型字符串,V8.3.6add，必传
    sourceCode: 'isv', // 业务方来源，例如 babel，类型字符串,V8.3.6add，必传
    keyImg: img, // 分享图片，类型字符串,V8.3.6add，必传
    keyId: url, // 活动标识，每个具体活动唯一，类型字符串,V8.3.6add,必传
    keyTitle: title, // 分享标题,类型字符串,V.3.6add，必传,
    keyContent: content, // 口令内容文案,V8.3.6add，必传，口令可以掺插在内容中间，需用在对应位置使用占位符“@@@@”，否则口令接在文案结尾
    keyOpenapp: `openapp.jdmobile://virtual?params=${JSON.stringify(openAppParams)}`, // 原生openApp协议，与url二者至少填写一个
    // acrossClient: '0', // 跨客户端支持 老：0:不可以，1:可以,非必传，默认不支持， 由0、1组成6位数字，每一位数字代表一个支持兑换口令的客户端,数字枚举值为：1-支持兑换/0-不支持兑换,数字安排为（从左至右）：第一位-京东商城，第二位-京东金融，第三位-京喜。后三位为拓展,如"100000"表示仅支持商城app打开
    // keySkuid: '', // skuId,V8.4.4 add，非必传 v8.4.4 add,如果非空，则强制不能跨客户端使用口令
    // keyVer: '', // 版本限制,原生必传,不传仅支持h5，格式{client1:version1,client2:version2},client指的是各个客户端对于android和ios的标识,version 支持起始版本，不填的client仅支持h5
    // keyType: 1, // v9.2.0 add 为1的时候，下发简版口令,默认下发带文案
  };
  const shareConfig = {
    title, // 分享标题
    content, // 分享内容
    url, // 分享链接
    img, // 分享图片
    channel, // 发送给微信好友
    callback, // 分享回调 // 点击微信/朋友圈/QQ等图标之后触发，不保证完成分享流程
    clickcallback, // 点击回调
    appCode: 'jApp', //  jApp - 商城 jXi - 京喜 jFinance - 京东金融 jLite - 京东极速版 liwo - 梨涡
    keyparam,
  };
  console.log('获取京口令分享配置', shareConfig);
  return shareConfig;
};

const getShareImage = (imageUrl?: string | null) => {
  if (imageUrl) {
    return imageUrl;
  }
  const shareConfig = sessionStorage.getItem(constant.LZ_SHARE_CONFIG);
  if (shareConfig) {
    return JSON.parse(shareConfig).shareImage;
  }
  return defaultShareImg;
};

// 获取h5分享配置
const getH5ShareConfig = (config: ShareConfig = {}) => {
  console.log('getH5ShareConfig');
  const title = config.title || '请您参与活动';
  const content = config.content || title;
  const orginUrl = config.shareUrl || window.location.href;
  const url = exchangeSubStr(orginUrl, DOMAIN.PROTECT, DOMAIN.COMMON);
  const img = config.imageUrl || defaultShareImg;
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const clickcallback = (() => {
    console.log('click callback');
    lzReportClick('v_head_share_click');
    config.afterShare && config.afterShare();
  });
  const channel = config.channel || defaultH5ShareChannel;
  const shareConfig = {
    title, // 分享标题
    content, // 分享内容
    url, // 分享链接
    img, // 分享图片
    channel, // 发送给微信好友
    callback, // 分享回调 // 点击微信/朋友圈/QQ等图标之后触发，不保证完成分享流程
    clickcallback, // 点击回调
    qrparam: null, // 二维码参数？？？
    timeline_title: title, // 朋友圈标题
  };
  console.log('获取JDh5分享配置', shareConfig);
  return shareConfig;
};

// 获取小程序分享配置
const getMiniAppShareConfig = (config: ShareConfig = {}) => {
  console.log('getMiniAppShareConfig');
  const title = config.title || '请您参与活动';
  const content = config.content || title;
  const originUrl = config.shareUrl || window.location.href;
  const url = exchangeSubStr(originUrl, DOMAIN.PROTECT, DOMAIN.COMMON);
  const img = getShareImage(config.imageUrl);
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const clickcallback = (() => {
    console.log('click callback');
    lzReportClick('v_head_share_click');
    config.afterShare && config.afterShare();
  });
  const shareUrl = encodeURIComponent(url);
  const channel = config.channel || defaultMpShareChannel;
  const shareConfig = {
    title,
    content,
    url: shareUrl,
    img,
    channel,
    callback,
    clickcallback,
    qrparam: null,
    timeline_title: '',
    mpId: 'gh_45b306365c3d',
    mpIconUrl: img, // 微信小程序分享的图片
    mpPath: `pages/h5_wv/sns/index?encode_url=${shareUrl}`,
    mpType: '0',
  };
  console.log('获取JD小程序分享配置', shareConfig);
  return shareConfig;
};

// 获取分享配置
const getShareConfig = (config: ShareConfig = {}) => {
  const type = config.type ?? sessionStorage.getItem(constant.LZ_SHARE_TYPE) ?? ShareType.CMD;
  switch (type) {
    case ShareType.CMD:
      return getCMDShareConfig(config);
    case ShareType.MINIAPP:
      return getMiniAppShareConfig(config);
    case ShareType.H5:
      return getH5ShareConfig(config);
    default:
      return getH5ShareConfig(config);
  }
};

// 设置H5分享
const shareH5WithSDK: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  console.log('设置H5WithSDK分享', config);
  const title = config.title ?? '请您参与活动';
  const content = config.content ?? title;
  const originUrl = config.shareUrl ?? window.location.href;
  const url = exchangeSubStr(originUrl, DOMAIN.PROTECT, DOMAIN.COMMON);
  const img = config.imageUrl ?? defaultShareImg;
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const clickcallback = (() => {
    console.log('click callback');
    lzReportClick('v_head_share_click');
    config.afterShare && config.afterShare();
  });
  const shareUrl = encodeURIComponent(url);
  const channel = config.channel || defaultMpShareChannel;
  console.log('Jdsdk.WebView.callSharePanel');
  window.jmfe.setShareInfo({
    title, // 分享标题
    content, // 分享内容
    url,
    channel,
    img,
    timeline_title: '',
    mpId: 'gh_45b306365c3d',
    mpIconUrl: img, // 微信小程序分享的图片
    mpPath: `pages/h5_wv/sns/index?encode_url=${shareUrl}`,
    mpType: '0',
    callback,
    clickcallback,
  });
};

// 设置微信h5分享
const shareH5inWeChat: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  const title = config.title ?? '请您参与活动';
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const shareConfig = {
    title, // 分享标题
    desc: config.content ?? title, // 分享描述
    link: config.shareUrl ?? window.location.href, // 分享链接,微信h5分享需要替换域名到isvjd.com
    imgUrl: getShareImage(config.imageUrl),
    success: () => callback(),
    fail: (error: any) => {
      console.log('设置微信h5分享 fail', error);
    },
  };
  console.log('获取微信jssdk接口授权');
  await wxSdkConfig(shareConfig.link, config.debug);
  console.log('设置微信h5分享', shareConfig);
  // // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
  window.wx.updateAppMessageShareData(shareConfig);
  // // 自定义“分享到朋友圈”及“分享到 QQ 空间”按钮的分享内容（1.4.0）
  window.wx.updateTimelineShareData(shareConfig);
  // // 获取“分享到腾讯微博”按钮点击状态及自定义分享内容接口
  // window.wx.onMenuShareWeibo(shareConfig);
  // 分享给朋友(废弃)
  window.wx.onMenuShareAppMessage(shareConfig);
  // 分享到朋友圈(废弃)
  window.wx.onMenuShareTimeline(shareConfig);
  // 分享到QQ(废弃)
  window.wx.onMenuShareQQ(shareConfig);
  // 分享到QQ空间(废弃)
  window.wx.onMenuShareQZone(shareConfig);
};

// 小程序分享
const shareInMiniProgram: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  console.log('设置微信小程序分享', config);
  const title = config.title ?? '请您参与活动';
  const originUrl = config.shareUrl ?? window.location.href;
  let url = exchangeSubStr(originUrl, DOMAIN.PROTECT, DOMAIN.COMMON);
  const img = config.imageUrl ?? defaultShareImg;
  const callback = (() => {
    console.log('share', config);
    lzReportClick('v_head_share');
    config.afterShare && config.afterShare();
  });
  const userPin = sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) || '';

  if (url.indexOf('shareuserid4minipg') < 0) {
    if (url.indexOf('?') > 0) {
      url = `${url}&shareuserid4minipg=${encodeURIComponent(userPin)}`;
    } else {
      url = `${url}?shareuserid4minipg=${encodeURIComponent(userPin)}`;
    }
  }

  // 小程序分享页面氛围图片尺寸：670*470
  const atmosimgurl = `${img.replace('/jfs/', '/s670x470_jfs/')}!cc_670x470`;

  // 小程序分享出去的围图片尺寸：500*400
  const shareimgurl = `${img.replace('/jfs/', '/s500x400_jfs/')}!cc_500x400`;

  const mpUrl = `/pages/h5share/index/index?atmosimgurl=${encodeURIComponent(atmosimgurl)}&sharetitle=${encodeURIComponent(title)}&shareurl=${encodeURIComponent(url)}&shareimgurl=${encodeURIComponent(shareimgurl)}&isautoback=1&noneBtnBg=0`;

  console.log('mpUrl', mpUrl);

  // @ts-ignore
  wx.miniProgram.navigateTo({
    url: mpUrl,
  });
  callback();
};

const jdMiniProgramPostMessage = (config: ShareConfig) => {
  // 仅当页面在京东小程序内的webview里面显示的时候有效
  // 分享内容配置
  // 属性  类型  必填  说明
  // title  string  否  转发标题
  // type  string  否  转发形式（1 - 京东小程序正式版；2 - 京东小程序体验版；京东App9.0.0开始不填或者其他值都会先判断是否有url参数，如果有打开分享后显示url对应页面，否则默认生成京东小程序官方的一个分享中间页面，点击可跳到京东app里面的对应小程序）
  // path  string  否  要打开小程序的页面路径，例如:'pages/index' ，开头不要加'/'，以下错误示范：'/page/index'
  // imageUrl  string  是  图片地址（小程序封面图或h5页封面）
  // channel  string  否  渠道（不写默认微信朋友，微信朋友圈），可用值有：Wxfriends,QQfriends,Wxmoments,QQzone,Sinaweibo
  // url  string  否  h5链接地址（h5分享填写，不填默认中间页）
  // desc  string  否  分享内容摘要
  // keyShareChannel  string  否  口令分享渠道，可用值有：Wxfriends,QQfriends,Wxmoments,QQzone,Sinaweibo，当需要口令分享时，需要配置此选项
  // localImageUrl  string  否  海报分享，本地图片地址（海报图片由开发者生成后将图片地址传入jdfile开头的格式）
  // onlineImageUrl  string  否  海报分享，网络图片地址（海报图片由开发者生成后将图片地址传入），注意：localImageUrl、onlineImageUrl建议开发者使用时只传一个值 如果传入两个值 优先localImageUrl

  // 目前不找到京东小程序webview嵌h5页面的时候，postMessage生效机制，为了避免页面初始化的时候设置头部分享失效，这里简单的间隔1秒发一次，最多5次
  let i = 5;
  const timer = setInterval(() => {
    const title = config.title || '请您参与活动';
    const desc = config.content || title;
    const url = exchangeSubStr(config.shareUrl || window.location.href, DOMAIN.PROTECT, DOMAIN.COMMON);
    const imageUrl = getShareImage(config.imageUrl);
    const channel = config.channel || defaultMpShareChannel;
    const path = `index?target=${url}`; // 超级大牌的小程序默认地址就是这个(这个是小程序的页面地址)
    const args = {
      path,
      title,
      desc,
      imageUrl,
      channel,
    };
    if (window.jd) {
      // 这里用延迟处理，等待桥接js生效
      window.jd.miniProgram.postMessage({
        data: {
          type: 'share',
          args,
        },
      });
      console.log('通知webview我要设置头部分享', config);
      --i;
    }
    if (i <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 设置头部分享
export const setHeaderShare: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  try {
    const client = getClientType();
    if (client === clientType.WECHAT) {
      const isMp = await isMiniProgram();
      console.log(isMp);
      if (isMp) {
        console.log('小程序');
        await shareH5WithSDK(config);
      } else {
        console.log('微信');
        await shareH5inWeChat(config);
      }
    } else if (client === clientType.JDAPP) {
      console.log('京东APP 设置头部分享');
      const shareConfig = debounceShareCallBack(getShareConfig(config));
      window.jmfe.setShareInfo(shareConfig);
      jdMiniProgramPostMessage(config);
    }
  } catch (e) {
    console.error(e);
  }
};

// 唤起分享
export const callShare: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  console.log('根据客户端和分享渠道，自动设置分享');
  const client: string = getClientType();

  if (client === clientType.WECHAT) {
    // 微信下禁止做分享任务
    showToast('微信环境不支持活动分享，如需分享请点击右上角“···"通过浏览器分享～');
    return;
  }
  if (config.shareUrl === null || config.shareUrl === undefined || config.shareUrl === '' || config.shareUrl === window.location.href) {
    const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) || '{}');
    config.shareUrl = `${process.env.VUE_APP_HOST}landing/share/?shareId=${shareConfig.shareId}`;
  }
  reportShareEvent();
  lzReportClick('v_hand_share');
  try {
    if (client === clientType.WECHAT) {
      const isMp = await isMiniProgram();
      if (isMp) {
        console.log('小程序');
        await shareInMiniProgram(config);
      } else {
        console.log('微信');
        await shareH5inWeChat(config);
      }
    } else if (client === clientType.JDAPP) {
      console.log('京东APP');
      console.log(window.jmfe, 'jmfe');

      const shareConfig = debounceShareCallBack(getShareConfig(config));
      window.jmfe.callSharePane(shareConfig);
    }
  } catch (e) {
    console.error(e);
  }
};

const loadImage = (src: string): Promise<HTMLImageElement> => new Promise((resolve, reject) => {
  const img = new Image();
  img.crossOrigin = 'Anonymous'; // Set crossOrigin attribute
  img.src = src;
  img.onload = () => resolve(img);
  img.onerror = reject;
});

const getQrCodeImage = (text: string) => new Promise<string>((resolve, reject) => {
  QRCode.toDataURL(text, { width: 200 }, (err: any, url: string) => {
    if (err) {
      reject(err);
    } else {
      resolve(url);
    }
  });
});

export const getPosterCanvas = async (posterConfig: SharePosterConfig) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Canvas context not available');
  const {
    width,
    height,
    shareUrl,
    bg,
    content,
    qr,
    quality = 0.5,
  } = posterConfig;

  const imgBg = await loadImage(bg.url);
  canvas.width = width || imgBg.width;
  canvas.height = height || imgBg.height;

  ctx.drawImage(imgBg, 0, 0, imgBg.width, imgBg.height);

  if (content && content.url) {
    const imgContent = await loadImage(content.url);
    ctx.drawImage(imgContent, content.x, content.y, content.w, content.h);
  }

  if (qr) {
    const qrUrl = qr.url ?? await getQrCodeImage(exchangeSubStr(shareUrl || window.location.href, DOMAIN.PROTECT, DOMAIN.COMMON));
    const img2 = await loadImage(qrUrl);
    ctx.drawImage(img2, qr.x, qr.y, qr.w, qr.h);
  }
  return canvas;
};

export const getPosterImage = async (posterConfig: SharePosterConfig) => {
  const canvas = await getPosterCanvas(posterConfig);
  const { quality = 0.5 } = posterConfig;
  const base64 = canvas.toDataURL('image/png', quality);
  console.log('base64', base64);
  return base64;
};

const uploadPosterImage = async (blob: Blob) => {
  // 创建 FormData 实例
  const file = await compressImage(blob, { quality: 0.5 });
  const formData = new FormData();
  formData.append('pictureCateId', '0');
  formData.append('file', file, 'poster.png'); // 'poster.png' 是文件名
  // 使用 httpRequest 上传文件
  return httpRequest.post('/common/uploadImg', formData, {
    headers: {
      'Content-Type': 'multipart/form-data', // 上传文件时需要设置这个头
    },
  });
};

export const getPosterServerUrl = async (posterConfig: SharePosterConfig) => {
  const canvas = await getPosterCanvas(posterConfig);
  const { quality = 0.5 } = posterConfig;

  return new Promise<string>((resolve, reject) => {
    canvas.toBlob(async (blob) => {
      if (blob) {
        try {
          const response = await uploadPosterImage(blob);
          console.log('response', response);
          resolve(response.data);
        } catch (error: any) {
          reject(error);
        }
      } else {
        reject(new Error('Canvas 转换为 Blob 失败'));
      }
    }, 'image/png', quality);
  });
};

// 唤起海报分享
export const callPosterShare: (config: ShareConfig) => Promise<void> = async (config: ShareConfig = {}) => {
  if (!config.poster) throw new Error('缺少海报配置');
  let posterImageUrl = null;
  // 如果是Android，转成serverUrl
  if (window.jmfe.isAndroid()) {
    posterImageUrl = await getPosterServerUrl(config.poster);
  } else {
    posterImageUrl = await getPosterImage(config.poster);
  }
  console.log('posterImageUrl', posterImageUrl);
  const shareConfig = debounceShareCallBack(getShareConfig(config));
  const opt = {
    ...shareConfig,
    channel: config.channel ?? 'QRCode',
    // channel: '',
    qrparam: {
      qr_direct: posterImageUrl,
    },
  };
  console.log('callPosterShare.opt', opt);
  window.jmfe.callSharePane(opt);
};
